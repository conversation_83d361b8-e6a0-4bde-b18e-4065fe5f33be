#!/usr/bin/env python3
"""
Test to understand Odoo's currency rate field direction
"""

def test_odoo_rate_direction():
    print("=== TESTING ODOO CURRENCY RATE DIRECTION ===")
    print()
    
    # From your screenshot:
    # USD shows: Unit per INR = 1.432162, INR per Unit = 0.698245
    # But we stored: 85.892311
    
    stored_rate = 85.892311  # What we stored in Odoo
    displayed_unit_per_inr = 1.432162  # What Odoo shows as "Unit per INR"
    displayed_inr_per_unit = 0.698245  # What Odoo shows as "INR per Unit"
    
    print(f"Rate we stored in DB: {stored_rate}")
    print(f"Odoo displays 'Unit per INR': {displayed_unit_per_inr}")
    print(f"Odoo displays 'INR per Unit': {displayed_inr_per_unit}")
    print()
    
    # Test hypothesis: Odoo rate field is inverse
    print("=== HYPOTHESIS: Odoo rate field is INVERSE ===")
    
    # If Odoo rate field means "1 foreign currency = X company currency"
    # Then for USD: rate = 85.89 means "1 USD = 85.89 INR"
    # Odoo would display:
    # - "Unit per INR" = 1/85.89 = 0.01164 (how many USD per 1 INR)
    # - "INR per Unit" = 85.89 (how many INR per 1 USD)
    
    expected_unit_per_inr = 1 / stored_rate
    expected_inr_per_unit = stored_rate
    
    print(f"If rate field means '1 USD = {stored_rate} INR':")
    print(f"  Expected 'Unit per INR': {expected_unit_per_inr:.9f}")
    print(f"  Expected 'INR per Unit': {expected_inr_per_unit:.6f}")
    print()
    
    print(f"But Odoo actually shows:")
    print(f"  Actual 'Unit per INR': {displayed_unit_per_inr:.9f}")
    print(f"  Actual 'INR per Unit': {displayed_inr_per_unit:.6f}")
    print()
    
    # Check if there's a relationship
    print("=== ANALYZING THE RELATIONSHIP ===")
    
    # Maybe Odoo rate field means the opposite?
    # If rate = 85.89 means "85.89 USD = 1 INR" (inverse)
    # Then:
    # - "Unit per INR" = 85.89 (how many USD per 1 INR)
    # - "INR per Unit" = 1/85.89 = 0.01164 (how many INR per 1 USD)
    
    if_inverse_unit_per_inr = stored_rate
    if_inverse_inr_per_unit = 1 / stored_rate
    
    print(f"If rate field means '{stored_rate} USD = 1 INR' (INVERSE):")
    print(f"  Expected 'Unit per INR': {if_inverse_unit_per_inr:.6f}")
    print(f"  Expected 'INR per Unit': {if_inverse_inr_per_unit:.9f}")
    print()
    
    # Check what matches
    unit_per_inr_matches = abs(displayed_unit_per_inr - expected_unit_per_inr) < 0.001
    inr_per_unit_matches = abs(displayed_inr_per_unit - expected_inr_per_unit) < 0.001
    
    inverse_unit_per_inr_matches = abs(displayed_unit_per_inr - if_inverse_unit_per_inr) < 0.001
    inverse_inr_per_unit_matches = abs(displayed_inr_per_unit - if_inverse_inr_per_unit) < 0.001
    
    print("=== RESULTS ===")
    print(f"Normal interpretation matches: Unit per INR={unit_per_inr_matches}, INR per Unit={inr_per_unit_matches}")
    print(f"Inverse interpretation matches: Unit per INR={inverse_unit_per_inr_matches}, INR per Unit={inverse_inr_per_unit_matches}")
    print()
    
    # Neither matches exactly, so let's see what the actual relationship is
    print("=== FINDING THE ACTUAL RELATIONSHIP ===")
    
    # What would give us the displayed values?
    # displayed_unit_per_inr = 1.432162
    # displayed_inr_per_unit = 0.698245
    
    # If Unit per INR = 1.432162, then 1 INR = 1.432162 USD
    # If INR per Unit = 0.698245, then 1 USD = 0.698245 INR
    
    # These are inverses of each other:
    inverse_check = 1 / displayed_unit_per_inr
    print(f"1 / Unit per INR = 1 / {displayed_unit_per_inr} = {inverse_check:.6f}")
    print(f"INR per Unit = {displayed_inr_per_unit:.6f}")
    print(f"Are they equal? {abs(inverse_check - displayed_inr_per_unit) < 0.001}")
    print()
    
    # So the displayed values are consistent with each other
    # But they don't match our stored rate
    
    # Let's see what rate would produce these displays
    # If "INR per Unit" = 0.698245 means "1 USD = 0.698245 INR"
    # Then the rate we should store is 0.698245
    
    rate_that_would_give_display = displayed_inr_per_unit
    print(f"Rate that would give current display: {rate_that_would_give_display}")
    print(f"Rate we actually stored: {stored_rate}")
    print(f"Ratio: {stored_rate / rate_that_would_give_display:.1f}")
    print()
    
    print("=== CONCLUSION ===")
    print("The Odoo rate field appears to work in the OPPOSITE direction!")
    print("We stored 85.89 thinking it means '1 USD = 85.89 INR'")
    print("But Odoo interprets it as '85.89 USD = 1 INR' or '1 USD = 1/85.89 INR'")
    print()
    print("SOLUTION: We need to store the INVERSE of our calculated rate!")

if __name__ == "__main__":
    test_odoo_rate_direction()
