#!/usr/bin/env python3
"""
Test script to verify the corrected currency rate calculation logic.
This script simulates the corrected logic to ensure rates are calculated properly.
"""

def test_corrected_currency_logic():
    """Test the corrected currency rate calculation logic"""
    
    print("=== TESTING CORRECTED CURRENCY RATE LOGIC ===")
    print()
    
    # Simulate API response from OpenExchangeRates
    api_response = {
        'base': 'USD',
        'rates': {
            'INR': 85.94,  # 1 USD = 85.94 INR
            'EUR': 0.85,   # 1 USD = 0.85 EUR
            'GBP': 0.79,   # 1 USD = 0.79 GBP
            'JPY': 150.25, # 1 USD = 150.25 JPY
        }
    }
    
    # Company settings
    company_currency = 'INR'
    base_currency = api_response['base']  # USD
    rates = api_response['rates']
    
    print(f"API Base Currency: {base_currency}")
    print(f"Company Currency: {company_currency}")
    print(f"API Rates: {rates}")
    print()
    
    # Get company currency rate from API
    usd_to_company_rate = rates[company_currency]  # 85.94
    print(f"USD to Company Rate: 1 USD = {usd_to_company_rate} {company_currency}")
    print()
    
    # Test currencies to process
    test_currencies = ['USD', 'EUR', 'GBP', 'JPY']
    
    for currency_code in test_currencies:
        print(f"--- Processing {currency_code} ---")
        
        if currency_code == company_currency:
            print(f"Skipping company currency: {currency_code}")
            continue
            
        # CORRECTED LOGIC: Store DIRECT rates (not inverse)
        if currency_code == base_currency:
            # This is USD
            # Company is INR, API says 1 USD = 85.94 INR
            # Odoo wants: 1 USD = ? INR, so rate = 85.94 (DIRECT)
            calculated_rate = usd_to_company_rate  # 85.94
            odoo_rate = calculated_rate  # 85.94 (DIRECT, not inverse!)
            print(f"USD calculation: 1 USD = {calculated_rate} INR")
            print(f"Odoo rate (DIRECT): 1 USD = {odoo_rate:.6f} INR")
            
        elif currency_code in rates:
            # This is another currency (EUR, GBP, etc.)
            # API says: 1 USD = 0.85 EUR, 1 USD = 85.94 INR
            # We want: 1 EUR = ? INR = 85.94 / 0.85 = 101.11 INR
            # Odoo wants: 1 EUR = ? INR = 101.11 INR (DIRECT!)
            api_rate = rates[currency_code]
            calculated_rate = usd_to_company_rate / api_rate  # Cross rate
            odoo_rate = calculated_rate  # DIRECT rate
            print(f"{currency_code} calculation: 1 {currency_code} = {calculated_rate:.6f} INR")
            print(f"Odoo rate (DIRECT): 1 {currency_code} = {odoo_rate:.6f} INR")
        
        print(f"Final rate to store in Odoo: {odoo_rate:.6f}")
        print()
    
    print("=== EXPECTED RESULTS IN ODOO INTERFACE ===")
    print("After applying this fix, Odoo should display:")
    print("- USD: ~85.94 INR per Unit (not 5,151 or 0.698)")
    print("- EUR: ~101.11 INR per Unit")
    print("- GBP: ~108.78 INR per Unit")
    print("- JPY: ~0.57 INR per Unit")
    print()
    print("This matches real-world exchange rates!")

if __name__ == "__main__":
    test_corrected_currency_logic()
