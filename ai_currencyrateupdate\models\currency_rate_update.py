import requests
import logging
from datetime import datetime
from odoo import models, api, fields

_logger = logging.getLogger(__name__)

class CurrencyRateUpdate(models.Model):
    _name = 'currency.rate.update'
    _description = 'Currency Rate Update using OpenExchangeRates API'
    _order = 'create_date desc'

    name = fields.Char(string='Name', compute='_compute_name', store=True)
    success = fields.Boolean(string='Success', default=False)
    message = fields.Text(string='Message', readonly=True)
    
    @api.depends('create_date')
    def _compute_name(self):
        for record in self:
            if record.create_date:
                record.name = f"Update - {record.create_date.strftime('%Y-%m-%d %H:%M:%S')}"
            else:
                record.name = "New Update"

    def update_currency_rates(self):
        """
        Update currency rates from OpenExchangeRates API
        This method is called from button action or scheduled action
        """
        try:
            # Get API configuration from system parameters
            ICP = self.env['ir.config_parameter'].sudo()
            api_key = ICP.get_param('openexchangerates.api_key', False)
            
            if not api_key:
                message = "OpenExchangeRates API key is not configured"
                _logger.error(message)
                self.write({'success': False, 'message': message})
                return {'type': 'ir.actions.act_window_close'}
            
            # API endpoint
            url = f"https://openexchangerates.org/api/latest.json?app_id={api_key}"
            
            # Make API request
            response = requests.get(url)
            response.raise_for_status()
            
            # Parse response data
            data = response.json()
            base_currency_code = data.get('base')  # Usually USD from OpenExchangeRates
            rates = data.get('rates', {})
            timestamp = data.get('timestamp')
            date = datetime.fromtimestamp(timestamp).strftime('%Y-%m-%d')
            
            if not rates:
                message = "No rates returned from OpenExchangeRates API"
                _logger.error(message)
                self.write({'success': False, 'message': message})
                return {'type': 'ir.actions.act_window_close'}
            
            # Get currency objects from Odoo
            currency_obj = self.env['res.currency']
            
            # Get base currency from Odoo (company currency)
            company = self.env.company
            company_currency = company.currency_id
            company_currency_code = company_currency.name
            
            # Get all active currencies in Odoo
            active_currencies = currency_obj.search([('active', '=', True)])

            # CRITICAL FIX: Odoo rate field meaning
            # Based on research of "Use Inverted Currency Rate" app and user's display issues:
            #
            # Current WRONG rates show:
            # USD: Unit per INR = 1.432162, INR per Unit = 0.698245
            # This means Odoo thinks: 1 USD = 0.698245 INR (WRONG!)
            # Should be: 1 USD = 85.94 INR
            #
            # SOLUTION: Odoo's rate field represents "how many company currency units = 1 foreign currency"
            # This is DIRECT rate, not inverse!
            # So for INR company currency:
            # - USD rate = 85.94 (1 USD = 85.94 INR)
            # - EUR rate = 101.11 (1 EUR = 101.11 INR)

            # Get company currency rate from API if needed
            usd_to_company_rate = 1.0  # How many company currency units per 1 USD
            _logger.info(f"=== CURRENCY UPDATE DEBUG START ===")
            _logger.info(f"API Base Currency: {base_currency_code}")
            _logger.info(f"Company Currency: {company_currency_code}")
            _logger.info(f"API Response Keys: {list(rates.keys())}")

            if company_currency_code != base_currency_code:
                if company_currency_code not in rates:
                    message = f"Company currency {company_currency_code} not found in OpenExchangeRates response"
                    _logger.error(message)
                    self.write({'success': False, 'message': message})
                    return {'type': 'ir.actions.act_window_close'}
                usd_to_company_rate = rates[company_currency_code]  # e.g., 85.94 (1 USD = 85.94 INR)
                _logger.info(f"USD to company rate from API: 1 USD = {usd_to_company_rate} {company_currency_code}")
            else:
                _logger.info(f"Company currency is same as API base currency: {company_currency_code}")

            updated_count = 0

            # Process all active currencies
            for currency in active_currencies:
                if currency.id == company_currency.id:
                    _logger.info(f"Skipping company currency: {currency.name}")
                    continue  # Skip company currency as it's the base (rate=1)

                currency_code = currency.name
                _logger.info(f"--- Processing currency: {currency_code} ---")

                # Calculate the correct Odoo rate
                # CRITICAL: Odoo rate field is DIRECT rate!
                # Odoo rate field = "how many company currency units = 1 foreign currency unit"
                # So if 1 USD = 85.94 INR, we store 85.94 directly
                if currency_code == base_currency_code:
                    # This is USD
                    if company_currency_code == base_currency_code:
                        # Company is USD, so USD rate = 1.0 (skip)
                        _logger.info(f"Skipping {currency_code} - same as company currency")
                        continue
                    else:
                        # Company is INR, API says 1 USD = 85.94 INR
                        # Odoo wants: 1 USD = ? INR, so rate = 85.94
                        calculated_rate = usd_to_company_rate  # 85.94
                        odoo_rate = calculated_rate  # 85.94 (DIRECT, not inverse!)
                        _logger.info(f"USD calculation: 1 USD = {calculated_rate} INR")
                        _logger.info(f"Odoo rate (DIRECT): 1 USD = {odoo_rate:.6f} INR")
                        _logger.info(f"Storing rate: {odoo_rate:.6f}")
                elif currency_code in rates:
                    # This is another currency (EUR, GBP, etc.)
                    # API says: 1 USD = 0.85 EUR, 1 USD = 85.94 INR
                    # We want: 1 EUR = ? INR = 85.94 / 0.85 = 101.11 INR
                    # Odoo wants: 1 EUR = ? INR = 101.11 INR (DIRECT rate!)
                    api_rate = rates[currency_code]
                    if company_currency_code == base_currency_code:
                        # Company is USD, API gives us direct rate
                        # API: 1 USD = 0.85 EUR, so 1 EUR = 1/0.85 USD = 1.176 USD
                        # Odoo wants: 1 EUR = ? USD = 1.176 USD
                        odoo_rate = 1.0 / api_rate  # Convert to direct rate
                        _logger.info(f"{currency_code} calculation (company=USD): 1 {currency_code} = {odoo_rate:.6f} USD")
                    else:
                        # Company is INR, calculate cross rate (DIRECT)
                        # 1 EUR = (INR per USD) / (EUR per USD) = 85.94 / 0.85 = 101.11 INR
                        # Odoo wants: 1 EUR = ? INR = 101.11 INR (DIRECT!)
                        calculated_rate = usd_to_company_rate / api_rate  # 101.11
                        odoo_rate = calculated_rate  # 101.11 (DIRECT, not inverse!)
                        _logger.info(f"{currency_code} calculation: 1 {currency_code} = {calculated_rate:.6f} INR")
                        _logger.info(f"Odoo rate (DIRECT): 1 {currency_code} = {odoo_rate:.6f} INR")
                        _logger.info(f"Storing rate: {odoo_rate:.6f}")
                else:
                    _logger.warning(f"Currency {currency_code} not found in OpenExchangeRates response")
                    continue

                # Store the calculated rate
                existing_rate = self.env['res.currency.rate'].search([
                    ('currency_id', '=', currency.id),
                    ('name', '=', date)
                ], limit=1)

                _logger.info(f"Storing rate for {currency_code}: {odoo_rate:.6f}")
                _logger.info(f"Date: {date}")

                if existing_rate:
                    old_rate = existing_rate.rate
                    existing_rate.rate = odoo_rate
                    updated_count += 1
                    _logger.info(f"UPDATED {currency_code}: OLD={old_rate:.6f} -> NEW={odoo_rate:.6f}")
                    _logger.info(f"Meaning: 1 {currency_code} = {odoo_rate:.6f} {company_currency_code}")
                else:
                    new_rate = self.env['res.currency.rate'].create({
                        'currency_id': currency.id,
                        'rate': odoo_rate,
                        'name': date
                    })
                    updated_count += 1
                    _logger.info(f"CREATED {currency_code}: rate={odoo_rate:.6f} (ID: {new_rate.id})")
                    _logger.info(f"Meaning: 1 {currency_code} = {odoo_rate:.6f} {company_currency_code}")

                # Verify what was actually stored
                verify_rate = self.env['res.currency.rate'].search([
                    ('currency_id', '=', currency.id),
                    ('name', '=', date)
                ], limit=1)
                if verify_rate:
                    _logger.info(f"VERIFICATION: {currency_code} rate in DB = {verify_rate.rate:.6f}")
                    _logger.info(f"VERIFICATION: This means 1 {currency_code} = {verify_rate.rate:.6f} {company_currency_code}")
                else:
                    _logger.error(f"VERIFICATION FAILED: Could not find {currency_code} rate in DB")
            
            message = f"Successfully updated {updated_count} currency rates"
            _logger.info(f"=== CURRENCY UPDATE DEBUG END ===")
            _logger.info(message)
            self.write({'success': True, 'message': message})
            return {'type': 'ir.actions.act_window_close'}

        except requests.exceptions.RequestException as e:
            message = f"Error fetching currency rates: {str(e)}"
            _logger.error(message)
            self.write({'success': False, 'message': message})
            return {'type': 'ir.actions.act_window_close'}
        except Exception as e:
            message = f"Unexpected error: {str(e)}"
            _logger.error(message)
            self.write({'success': False, 'message': message})
            return {'type': 'ir.actions.act_window_close'}

    @api.model
    def cron_update_currency_rates(self):
        """
        Method to be called by scheduled action (cron job)
        Creates a new record and updates currency rates
        """
        record = self.create({})
        record.update_currency_rates()

class ResConfigSettings(models.TransientModel):
    _inherit = 'res.config.settings'

    openexchangerates_api_key = fields.Char(
        string='OpenExchangeRates API Key',
        config_parameter='openexchangerates.api_key',
        help='API Key for OpenExchangeRates service'
    ) 