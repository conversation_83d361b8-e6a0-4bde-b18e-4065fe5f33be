#!/usr/bin/env python3
"""
Test script to verify currency rate calculations are correct
"""

def test_currency_calculations():
    # Mock API response (similar to OpenExchangeRates)
    api_response = {
        'base': 'USD',
        'rates': {
            'INR': 85.94,    # 1 USD = 85.94 INR
            'EUR': 0.85,     # 1 USD = 0.85 EUR  
            'GBP': 0.79,     # 1 USD = 0.79 GBP
            'CAD': 1.35,     # 1 USD = 1.35 CAD
        }
    }
    
    # Test scenario: Company currency is INR
    base_currency_code = api_response['base']  # USD
    company_currency_code = 'INR'
    rates = api_response['rates']
    
    # Get USD to company rate
    usd_to_company_rate = rates[company_currency_code]  # 85.94
    
    print("=== CURRENCY RATE CALCULATION TEST ===")
    print(f"API Base Currency: {base_currency_code}")
    print(f"Company Currency: {company_currency_code}")
    print(f"USD to Company Rate: 1 {base_currency_code} = {usd_to_company_rate} {company_currency_code}")
    print()
    
    # Test currencies
    currencies_to_test = ['USD', 'EUR', 'GBP', 'CAD']
    
    for currency_code in currencies_to_test:
        print(f"=== {currency_code} Rate Calculation ===")
        
        if currency_code == base_currency_code:
            # This is USD - company is INR
            # API says: 1 USD = 85.94 INR
            # Odoo rate should be: 85.94 (meaning 1 USD = 85.94 INR)
            odoo_rate = usd_to_company_rate
            print(f"Branch: USD (base currency)")
            print(f"Odoo rate: {odoo_rate}")
            print(f"Meaning: 1 {currency_code} = {odoo_rate} {company_currency_code}")
            
        elif currency_code in rates:
            # Cross rate calculation
            # API: 1 USD = 0.85 EUR, 1 USD = 85.94 INR
            # We want: 1 EUR = ? INR
            # Answer: 1 EUR = (85.94 INR/USD) / (0.85 EUR/USD) = 101.11 INR
            api_rate = rates[currency_code]
            odoo_rate = usd_to_company_rate / api_rate
            print(f"Branch: Cross rate calculation")
            print(f"API rate: 1 USD = {api_rate} {currency_code}")
            print(f"Calculation: {usd_to_company_rate} ÷ {api_rate} = {odoo_rate:.6f}")
            print(f"Odoo rate: {odoo_rate:.6f}")
            print(f"Meaning: 1 {currency_code} = {odoo_rate:.6f} {company_currency_code}")
        
        print()
    
    print("=== VERIFICATION ===")
    print("Expected results:")
    print("- USD rate: ~85.94 (1 USD = 85.94 INR)")
    print("- EUR rate: ~101.11 (1 EUR = 101.11 INR)")  
    print("- GBP rate: ~108.78 (1 GBP = 108.78 INR)")
    print("- CAD rate: ~63.66 (1 CAD = 63.66 INR)")
    print()
    
    # Verify with actual calculations
    print("Manual verification:")
    print(f"EUR: {85.94 / 0.85:.2f}")
    print(f"GBP: {85.94 / 0.79:.2f}")
    print(f"CAD: {85.94 / 1.35:.2f}")

if __name__ == "__main__":
    test_currency_calculations()
